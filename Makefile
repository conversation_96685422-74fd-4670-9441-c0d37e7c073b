# Linux等保基线核查工具 Makefile

# 变量定义
APP_NAME = linux-security-checker
VERSION = v1.0.0
BUILD_TIME = $(shell date +%Y-%m-%d_%H:%M:%S)
GO_VERSION = $(shell go version | awk '{print $$3}')

# 编译标志
LDFLAGS = -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GoVersion=$(GO_VERSION)"

# 默认目标
.PHONY: all
all: build

# 安装依赖
.PHONY: deps
deps:
	go mod tidy
	go mod download

# 构建
.PHONY: build
build: deps
	go build $(LDFLAGS) -o $(APP_NAME) ./cmd

# 交叉编译Windows版本（简化版，不包含GUI）
.PHONY: build-windows-simple
build-windows-simple:
	@echo "编译Windows简化版本（无GUI）..."
	GOOS=windows GOARCH=amd64 go build -ldflags="-s -w" -o $(APP_NAME)-simple.exe -tags nogui ./cmd

# 交叉编译Windows版本（完整GUI版本，需要CGO）
.PHONY: build-windows
build-windows:
	@echo "编译Windows完整版本（包含GUI）..."
	@echo "注意: 需要CGO支持和mingw工具链"
	GOOS=windows GOARCH=amd64 CGO_ENABLED=1 go build $(LDFLAGS) -o $(APP_NAME).exe ./cmd

# 交叉编译Linux版本
.PHONY: build-linux
build-linux: deps
	GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(APP_NAME)-linux ./cmd

# 交叉编译macOS版本
.PHONY: build-darwin
build-darwin: deps
	GOOS=darwin GOARCH=amd64 go build $(LDFLAGS) -o $(APP_NAME)-darwin ./cmd

# 构建所有平台版本
.PHONY: build-all
build-all: build-windows build-linux build-darwin

# 运行
.PHONY: run
run: deps
	go run ./cmd

# 测试
.PHONY: test
test:
	go test -v ./...

# 测试覆盖率
.PHONY: test-coverage
test-coverage:
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# 代码格式化
.PHONY: fmt
fmt:
	go fmt ./...

# 代码检查
.PHONY: vet
vet:
	go vet ./...

# 清理
.PHONY: clean
clean:
	rm -f $(APP_NAME)
	rm -f $(APP_NAME).exe
	rm -f $(APP_NAME)-linux
	rm -f $(APP_NAME)-darwin
	rm -f coverage.out
	rm -f coverage.html

# 创建目录结构
.PHONY: init-dirs
init-dirs:
	mkdir -p data
	mkdir -p exports
	mkdir -p logs

# 安装开发工具
.PHONY: install-tools
install-tools:
	go install fyne.io/fyne/v2/cmd/fyne@latest

# 使用fyne打包
.PHONY: package-windows
package-windows: deps
	fyne package -os windows -icon assets/icon.png

# 帮助
.PHONY: help
help:
	@echo "可用的make目标:"
	@echo "  deps           - 安装依赖"
	@echo "  build          - 构建当前平台版本"
	@echo "  build-windows  - 交叉编译Windows版本"
	@echo "  build-linux    - 交叉编译Linux版本"
	@echo "  build-darwin   - 交叉编译macOS版本"
	@echo "  build-all      - 构建所有平台版本"
	@echo "  run            - 运行程序"
	@echo "  test           - 运行测试"
	@echo "  test-coverage  - 运行测试并生成覆盖率报告"
	@echo "  fmt            - 格式化代码"
	@echo "  vet            - 代码检查"
	@echo "  clean          - 清理构建文件"
	@echo "  init-dirs      - 创建必要的目录"
	@echo "  install-tools  - 安装开发工具"
	@echo "  package-windows- 使用fyne打包Windows版本"
	@echo "  help           - 显示此帮助信息"
