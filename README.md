# Linux等保基线核查工具

一款基于Go语言和Fyne GUI框架开发的Linux等保基线核查工具，支持单主机和多主机核查，提供直观的图形界面和完整的核查结果导出功能。

## 功能特性

- 🖥️ **单主机核查**: 对单台Linux主机进行等保基线核查
- 🏢 **多主机核查**: 创建主机组，批量核查多台主机
- 📊 **结果管理**: 查看、分析和导出核查结果
- 🎨 **侧边栏导航**: 提供清晰的功能导航界面
- 📄 **多格式导出**: 支持PDF、Excel、HTML格式报告导出
- 🔒 **安全连接**: 支持SSH密码和密钥认证
- ⚡ **并发处理**: 支持多主机并发核查

## 技术栈

- **编程语言**: Go 1.21+
- **GUI框架**: Fyne v2.6+
- **SSH连接**: golang.org/x/crypto/ssh
- **数据存储**: SQLite3
- **配置管理**: YAML

## 项目结构

```
linux-security-checker/
├── cmd/                    # 程序入口
│   └── main.go
├── internal/               # 内部包
│   ├── gui/               # GUI相关
│   ├── model/             # 数据模型
│   ├── service/           # 业务逻辑
│   ├── repository/        # 数据访问
│   └── config/            # 配置管理
├── assets/                # 资源文件
│   ├── rules/             # 等保基线规则
│   └── templates/         # 报告模板
├── docs/                  # 文档
├── go.mod
└── go.sum
```

## 快速开始

### 环境要求

- Go 1.21 或更高版本
- Linux/Windows/macOS 操作系统

### 安装依赖

```bash
go mod tidy
```

### 运行程序

```bash
go run cmd/main.go
```

### 编译

```bash
# 编译当前平台版本（GUI版本）
go build -o linux-security-checker ./cmd

# 交叉编译Windows版本（简化版，无GUI）
GOOS=windows GOARCH=amd64 go build -ldflags="-s -w" -tags nogui -o linux-security-checker.exe ./cmd

# 使用Makefile编译
make build-windows-simple  # Windows简化版
make build-windows         # Windows完整版（需要CGO和mingw）
```

## 开发状态

项目当前处于开发阶段，已完成：

- ✅ 项目初始化和环境搭建
- ✅ 基础项目结构创建
- ✅ 数据模型定义
- ✅ 配置管理系统
- 🚧 GUI界面开发（进行中）
- 🚧 核查引擎开发（计划中）

## 贡献

欢迎提交Issue和Pull Request来帮助改进这个项目。

## 许可证

本项目采用MIT许可证。详见 [LICENSE](LICENSE) 文件。

## 联系方式

如有问题或建议，请通过Issue联系我们。
