package config

import (
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// Config 应用配置结构体
type Config struct {
	Database DatabaseConfig `yaml:"database"`
	SSH      SSHConfig      `yaml:"ssh"`
	GUI      GUIConfig      `yaml:"gui"`
	Export   ExportConfig   `yaml:"export"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type string `yaml:"type"` // sqlite, mysql, postgres
	Path string `yaml:"path"` // for sqlite
	Host string `yaml:"host"`
	Port int    `yaml:"port"`
	Name string `yaml:"name"`
	User string `yaml:"user"`
	Pass string `yaml:"pass"`
}

// SSHConfig SSH连接配置
type SSHConfig struct {
	Timeout         int `yaml:"timeout"`          // 连接超时时间(秒)
	MaxConcurrency  int `yaml:"max_concurrency"`  // 最大并发连接数
	RetryCount      int `yaml:"retry_count"`      // 重试次数
	CommandTimeout  int `yaml:"command_timeout"`  // 命令执行超时时间(秒)
}

// GUIConfig GUI配置
type GUIConfig struct {
	Theme      string `yaml:"theme"`       // light, dark
	Language   string `yaml:"language"`    // zh, en
	WindowSize struct {
		Width  int `yaml:"width"`
		Height int `yaml:"height"`
	} `yaml:"window_size"`
}

// ExportConfig 导出配置
type ExportConfig struct {
	DefaultFormat string `yaml:"default_format"` // pdf, excel, html
	OutputDir     string `yaml:"output_dir"`
	TemplateDir   string `yaml:"template_dir"`
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		Database: DatabaseConfig{
			Type: "sqlite",
			Path: "./data/security_checker.db",
		},
		SSH: SSHConfig{
			Timeout:        30,
			MaxConcurrency: 10,
			RetryCount:     3,
			CommandTimeout: 60,
		},
		GUI: GUIConfig{
			Theme:    "light",
			Language: "zh",
			WindowSize: struct {
				Width  int `yaml:"width"`
				Height int `yaml:"height"`
			}{
				Width:  1200,
				Height: 800,
			},
		},
		Export: ExportConfig{
			DefaultFormat: "pdf",
			OutputDir:     "./exports",
			TemplateDir:   "./assets/templates",
		},
	}
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	// 如果配置文件不存在，创建默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		config := DefaultConfig()
		if err := SaveConfig(config, configPath); err != nil {
			return nil, err
		}
		return config, nil
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, err
	}

	return &config, nil
}

// SaveConfig 保存配置文件
func SaveConfig(config *Config, configPath string) error {
	// 确保目录存在
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	data, err := yaml.Marshal(config)
	if err != nil {
		return err
	}

	return os.WriteFile(configPath, data, 0644)
}
