package repository

import (
	"database/sql"
	"fmt"
	"time"

	"linux-security-checker/internal/model"
)

// GroupRepository 主机组数据访问层
type GroupRepository struct {
	db *sql.DB
}

// NewGroupRepository 创建主机组仓库实例
func NewGroupRepository(db *sql.DB) *GroupRepository {
	return &GroupRepository{db: db}
}

// Create 创建主机组
func (r *GroupRepository) Create(group *model.HostGroup) error {
	tx, err := r.db.Begin()
	if err != nil {
		return fmt.Errorf("开始事务失败: %v", err)
	}
	defer tx.Rollback()

	// 创建主机组
	query := `
		INSERT INTO host_groups (name, description, created_at, updated_at)
		VALUES (?, ?, ?, ?)
	`
	
	now := time.Now()
	group.CreatedAt = now
	group.UpdatedAt = now

	result, err := tx.Exec(query, group.Name, group.Description, group.CreatedAt, group.UpdatedAt)
	if err != nil {
		return fmt.Errorf("创建主机组失败: %v", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取主机组ID失败: %v", err)
	}

	group.ID = int(id)

	// 添加主机关系
	if len(group.HostIDs) > 0 {
		if err := r.addHostsToGroupTx(tx, group.ID, group.HostIDs); err != nil {
			return err
		}
	}

	return tx.Commit()
}

// GetByID 根据ID获取主机组
func (r *GroupRepository) GetByID(id int) (*model.HostGroup, error) {
	query := `
		SELECT id, name, description, created_at, updated_at
		FROM host_groups WHERE id = ?
	`

	group := &model.HostGroup{}
	err := r.db.QueryRow(query, id).Scan(
		&group.ID, &group.Name, &group.Description, &group.CreatedAt, &group.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("主机组不存在")
		}
		return nil, fmt.Errorf("查询主机组失败: %v", err)
	}

	// 获取主机ID列表
	hostIDs, err := r.getHostIDsByGroupID(group.ID)
	if err != nil {
		return nil, err
	}
	group.HostIDs = hostIDs

	return group, nil
}

// GetByName 根据名称获取主机组
func (r *GroupRepository) GetByName(name string) (*model.HostGroup, error) {
	query := `
		SELECT id, name, description, created_at, updated_at
		FROM host_groups WHERE name = ?
	`

	group := &model.HostGroup{}
	err := r.db.QueryRow(query, name).Scan(
		&group.ID, &group.Name, &group.Description, &group.CreatedAt, &group.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("主机组不存在")
		}
		return nil, fmt.Errorf("查询主机组失败: %v", err)
	}

	// 获取主机ID列表
	hostIDs, err := r.getHostIDsByGroupID(group.ID)
	if err != nil {
		return nil, err
	}
	group.HostIDs = hostIDs

	return group, nil
}

// GetAll 获取所有主机组
func (r *GroupRepository) GetAll() ([]*model.HostGroup, error) {
	query := `
		SELECT id, name, description, created_at, updated_at
		FROM host_groups ORDER BY created_at DESC
	`

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询主机组列表失败: %v", err)
	}
	defer rows.Close()

	var groups []*model.HostGroup
	for rows.Next() {
		group := &model.HostGroup{}
		err := rows.Scan(
			&group.ID, &group.Name, &group.Description, &group.CreatedAt, &group.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描主机组数据失败: %v", err)
		}

		// 获取主机ID列表
		hostIDs, err := r.getHostIDsByGroupID(group.ID)
		if err != nil {
			return nil, err
		}
		group.HostIDs = hostIDs

		groups = append(groups, group)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历主机组数据失败: %v", err)
	}

	return groups, nil
}

// Update 更新主机组
func (r *GroupRepository) Update(group *model.HostGroup) error {
	tx, err := r.db.Begin()
	if err != nil {
		return fmt.Errorf("开始事务失败: %v", err)
	}
	defer tx.Rollback()

	// 更新主机组基本信息
	query := `
		UPDATE host_groups 
		SET name = ?, description = ?, updated_at = ?
		WHERE id = ?
	`

	group.UpdatedAt = time.Now()

	result, err := tx.Exec(query, group.Name, group.Description, group.UpdatedAt, group.ID)
	if err != nil {
		return fmt.Errorf("更新主机组失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新行数失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("主机组不存在")
	}

	// 删除旧的主机关系
	if err := r.removeAllHostsFromGroupTx(tx, group.ID); err != nil {
		return err
	}

	// 添加新的主机关系
	if len(group.HostIDs) > 0 {
		if err := r.addHostsToGroupTx(tx, group.ID, group.HostIDs); err != nil {
			return err
		}
	}

	return tx.Commit()
}

// Delete 删除主机组
func (r *GroupRepository) Delete(id int) error {
	query := "DELETE FROM host_groups WHERE id = ?"

	result, err := r.db.Exec(query, id)
	if err != nil {
		return fmt.Errorf("删除主机组失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取删除行数失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("主机组不存在")
	}

	return nil
}

// AddHostsToGroup 向主机组添加主机
func (r *GroupRepository) AddHostsToGroup(groupID int, hostIDs []int) error {
	tx, err := r.db.Begin()
	if err != nil {
		return fmt.Errorf("开始事务失败: %v", err)
	}
	defer tx.Rollback()

	if err := r.addHostsToGroupTx(tx, groupID, hostIDs); err != nil {
		return err
	}

	return tx.Commit()
}

// RemoveHostsFromGroup 从主机组移除主机
func (r *GroupRepository) RemoveHostsFromGroup(groupID int, hostIDs []int) error {
	if len(hostIDs) == 0 {
		return nil
	}

	query := "DELETE FROM host_group_relations WHERE group_id = ? AND host_id IN ("
	args := []interface{}{groupID}
	
	for i, hostID := range hostIDs {
		if i > 0 {
			query += ","
		}
		query += "?"
		args = append(args, hostID)
	}
	query += ")"

	_, err := r.db.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("从主机组移除主机失败: %v", err)
	}

	return nil
}

// getHostIDsByGroupID 获取主机组中的主机ID列表
func (r *GroupRepository) getHostIDsByGroupID(groupID int) ([]int, error) {
	query := "SELECT host_id FROM host_group_relations WHERE group_id = ? ORDER BY host_id"
	
	rows, err := r.db.Query(query, groupID)
	if err != nil {
		return nil, fmt.Errorf("查询主机组关系失败: %v", err)
	}
	defer rows.Close()

	var hostIDs []int
	for rows.Next() {
		var hostID int
		if err := rows.Scan(&hostID); err != nil {
			return nil, fmt.Errorf("扫描主机ID失败: %v", err)
		}
		hostIDs = append(hostIDs, hostID)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历主机ID失败: %v", err)
	}

	return hostIDs, nil
}

// addHostsToGroupTx 在事务中向主机组添加主机
func (r *GroupRepository) addHostsToGroupTx(tx *sql.Tx, groupID int, hostIDs []int) error {
	if len(hostIDs) == 0 {
		return nil
	}

	query := "INSERT OR IGNORE INTO host_group_relations (group_id, host_id) VALUES (?, ?)"
	stmt, err := tx.Prepare(query)
	if err != nil {
		return fmt.Errorf("准备添加主机关系语句失败: %v", err)
	}
	defer stmt.Close()

	for _, hostID := range hostIDs {
		if _, err := stmt.Exec(groupID, hostID); err != nil {
			return fmt.Errorf("添加主机关系失败: %v", err)
		}
	}

	return nil
}

// removeAllHostsFromGroupTx 在事务中移除主机组的所有主机
func (r *GroupRepository) removeAllHostsFromGroupTx(tx *sql.Tx, groupID int) error {
	query := "DELETE FROM host_group_relations WHERE group_id = ?"
	_, err := tx.Exec(query, groupID)
	if err != nil {
		return fmt.Errorf("移除主机组所有主机失败: %v", err)
	}
	return nil
}
