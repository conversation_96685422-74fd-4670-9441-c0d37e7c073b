package repository

import (
	"database/sql"
	"fmt"

	"linux-security-checker/internal/model"
)

// RuleRepository 核查规则数据访问层
type RuleRepository struct {
	db *sql.DB
}

// NewRuleRepository 创建规则仓库实例
func NewRuleRepository(db *sql.DB) *RuleRepository {
	return &RuleRepository{db: db}
}

// Create 创建核查规则
func (r *RuleRepository) Create(rule *model.CheckRule) error {
	query := `
		INSERT INTO check_rules (name, category, command, expected_result, description, severity, enabled)
		VALUES (?, ?, ?, ?, ?, ?, ?)
	`

	result, err := r.db.Exec(query, rule.Name, rule.Category, rule.Command, 
		rule.ExpectedResult, rule.Description, rule.Severity, rule.Enabled)
	if err != nil {
		return fmt.Errorf("创建核查规则失败: %v", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取规则ID失败: %v", err)
	}

	rule.ID = int(id)
	return nil
}

// GetByID 根据ID获取核查规则
func (r *RuleRepository) GetByID(id int) (*model.CheckRule, error) {
	query := `
		SELECT id, name, category, command, expected_result, description, severity, enabled
		FROM check_rules WHERE id = ?
	`

	rule := &model.CheckRule{}
	err := r.db.QueryRow(query, id).Scan(
		&rule.ID, &rule.Name, &rule.Category, &rule.Command,
		&rule.ExpectedResult, &rule.Description, &rule.Severity, &rule.Enabled,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("核查规则不存在")
		}
		return nil, fmt.Errorf("查询核查规则失败: %v", err)
	}

	return rule, nil
}

// GetByName 根据名称获取核查规则
func (r *RuleRepository) GetByName(name string) (*model.CheckRule, error) {
	query := `
		SELECT id, name, category, command, expected_result, description, severity, enabled
		FROM check_rules WHERE name = ?
	`

	rule := &model.CheckRule{}
	err := r.db.QueryRow(query, name).Scan(
		&rule.ID, &rule.Name, &rule.Category, &rule.Command,
		&rule.ExpectedResult, &rule.Description, &rule.Severity, &rule.Enabled,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("核查规则不存在")
		}
		return nil, fmt.Errorf("查询核查规则失败: %v", err)
	}

	return rule, nil
}

// GetAll 获取所有核查规则
func (r *RuleRepository) GetAll() ([]*model.CheckRule, error) {
	query := `
		SELECT id, name, category, command, expected_result, description, severity, enabled
		FROM check_rules ORDER BY category, name
	`

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询核查规则列表失败: %v", err)
	}
	defer rows.Close()

	var rules []*model.CheckRule
	for rows.Next() {
		rule := &model.CheckRule{}
		err := rows.Scan(
			&rule.ID, &rule.Name, &rule.Category, &rule.Command,
			&rule.ExpectedResult, &rule.Description, &rule.Severity, &rule.Enabled,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描核查规则数据失败: %v", err)
		}
		rules = append(rules, rule)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历核查规则数据失败: %v", err)
	}

	return rules, nil
}

// GetEnabled 获取所有启用的核查规则
func (r *RuleRepository) GetEnabled() ([]*model.CheckRule, error) {
	query := `
		SELECT id, name, category, command, expected_result, description, severity, enabled
		FROM check_rules WHERE enabled = 1 ORDER BY category, name
	`

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询启用核查规则失败: %v", err)
	}
	defer rows.Close()

	var rules []*model.CheckRule
	for rows.Next() {
		rule := &model.CheckRule{}
		err := rows.Scan(
			&rule.ID, &rule.Name, &rule.Category, &rule.Command,
			&rule.ExpectedResult, &rule.Description, &rule.Severity, &rule.Enabled,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描核查规则数据失败: %v", err)
		}
		rules = append(rules, rule)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历核查规则数据失败: %v", err)
	}

	return rules, nil
}

// GetByCategory 根据分类获取核查规则
func (r *RuleRepository) GetByCategory(category string) ([]*model.CheckRule, error) {
	query := `
		SELECT id, name, category, command, expected_result, description, severity, enabled
		FROM check_rules WHERE category = ? ORDER BY name
	`

	rows, err := r.db.Query(query, category)
	if err != nil {
		return nil, fmt.Errorf("查询分类核查规则失败: %v", err)
	}
	defer rows.Close()

	var rules []*model.CheckRule
	for rows.Next() {
		rule := &model.CheckRule{}
		err := rows.Scan(
			&rule.ID, &rule.Name, &rule.Category, &rule.Command,
			&rule.ExpectedResult, &rule.Description, &rule.Severity, &rule.Enabled,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描核查规则数据失败: %v", err)
		}
		rules = append(rules, rule)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历核查规则数据失败: %v", err)
	}

	return rules, nil
}

// GetBySeverity 根据严重程度获取核查规则
func (r *RuleRepository) GetBySeverity(severity string) ([]*model.CheckRule, error) {
	query := `
		SELECT id, name, category, command, expected_result, description, severity, enabled
		FROM check_rules WHERE severity = ? ORDER BY category, name
	`

	rows, err := r.db.Query(query, severity)
	if err != nil {
		return nil, fmt.Errorf("查询严重程度核查规则失败: %v", err)
	}
	defer rows.Close()

	var rules []*model.CheckRule
	for rows.Next() {
		rule := &model.CheckRule{}
		err := rows.Scan(
			&rule.ID, &rule.Name, &rule.Category, &rule.Command,
			&rule.ExpectedResult, &rule.Description, &rule.Severity, &rule.Enabled,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描核查规则数据失败: %v", err)
		}
		rules = append(rules, rule)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历核查规则数据失败: %v", err)
	}

	return rules, nil
}

// Update 更新核查规则
func (r *RuleRepository) Update(rule *model.CheckRule) error {
	query := `
		UPDATE check_rules 
		SET name = ?, category = ?, command = ?, expected_result = ?, description = ?, severity = ?, enabled = ?
		WHERE id = ?
	`

	result, err := r.db.Exec(query, rule.Name, rule.Category, rule.Command,
		rule.ExpectedResult, rule.Description, rule.Severity, rule.Enabled, rule.ID)
	if err != nil {
		return fmt.Errorf("更新核查规则失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新行数失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("核查规则不存在")
	}

	return nil
}

// Delete 删除核查规则
func (r *RuleRepository) Delete(id int) error {
	query := "DELETE FROM check_rules WHERE id = ?"

	result, err := r.db.Exec(query, id)
	if err != nil {
		return fmt.Errorf("删除核查规则失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取删除行数失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("核查规则不存在")
	}

	return nil
}

// SetEnabled 设置规则启用状态
func (r *RuleRepository) SetEnabled(id int, enabled bool) error {
	query := "UPDATE check_rules SET enabled = ? WHERE id = ?"

	result, err := r.db.Exec(query, enabled, id)
	if err != nil {
		return fmt.Errorf("设置规则状态失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新行数失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("核查规则不存在")
	}

	return nil
}

// GetCategories 获取所有规则分类
func (r *RuleRepository) GetCategories() ([]string, error) {
	query := "SELECT DISTINCT category FROM check_rules ORDER BY category"

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询规则分类失败: %v", err)
	}
	defer rows.Close()

	var categories []string
	for rows.Next() {
		var category string
		if err := rows.Scan(&category); err != nil {
			return nil, fmt.Errorf("扫描分类数据失败: %v", err)
		}
		categories = append(categories, category)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历分类数据失败: %v", err)
	}

	return categories, nil
}

// Count 获取规则总数
func (r *RuleRepository) Count() (int, error) {
	query := "SELECT COUNT(*) FROM check_rules"
	
	var count int
	err := r.db.QueryRow(query).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("获取规则总数失败: %v", err)
	}

	return count, nil
}

// CountByCategory 获取分类规则数量
func (r *RuleRepository) CountByCategory(category string) (int, error) {
	query := "SELECT COUNT(*) FROM check_rules WHERE category = ?"
	
	var count int
	err := r.db.QueryRow(query, category).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("获取分类规则数量失败: %v", err)
	}

	return count, nil
}
