package repository

import (
	"database/sql"
	"fmt"
	"time"

	"linux-security-checker/internal/model"
)

// ResultRepository 核查结果数据访问层
type ResultRepository struct {
	db *sql.DB
}

// NewResultRepository 创建结果仓库实例
func NewResultRepository(db *sql.DB) *ResultRepository {
	return &ResultRepository{db: db}
}

// CreateTask 创建核查任务
func (r *ResultRepository) CreateTask(task *model.CheckTask) error {
	query := `
		INSERT INTO check_tasks (id, name, type, target_id, status, progress, created_at)
		VALUES (?, ?, ?, ?, ?, ?, ?)
	`

	task.CreatedAt = time.Now()

	_, err := r.db.Exec(query, task.ID, task.Name, task.Type, task.TargetID, 
		task.Status, task.Progress, task.CreatedAt)
	if err != nil {
		return fmt.Errorf("创建核查任务失败: %v", err)
	}

	return nil
}

// GetTaskByID 根据ID获取核查任务
func (r *ResultRepository) GetTaskByID(id string) (*model.CheckTask, error) {
	query := `
		SELECT id, name, type, target_id, status, progress, created_at, completed_at
		FROM check_tasks WHERE id = ?
	`

	task := &model.CheckTask{}
	var completedAt sql.NullTime

	err := r.db.QueryRow(query, id).Scan(
		&task.ID, &task.Name, &task.Type, &task.TargetID,
		&task.Status, &task.Progress, &task.CreatedAt, &completedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("核查任务不存在")
		}
		return nil, fmt.Errorf("查询核查任务失败: %v", err)
	}

	if completedAt.Valid {
		task.CompletedAt = &completedAt.Time
	}

	return task, nil
}

// GetAllTasks 获取所有核查任务
func (r *ResultRepository) GetAllTasks() ([]*model.CheckTask, error) {
	query := `
		SELECT id, name, type, target_id, status, progress, created_at, completed_at
		FROM check_tasks ORDER BY created_at DESC
	`

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询核查任务列表失败: %v", err)
	}
	defer rows.Close()

	var tasks []*model.CheckTask
	for rows.Next() {
		task := &model.CheckTask{}
		var completedAt sql.NullTime

		err := rows.Scan(
			&task.ID, &task.Name, &task.Type, &task.TargetID,
			&task.Status, &task.Progress, &task.CreatedAt, &completedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描核查任务数据失败: %v", err)
		}

		if completedAt.Valid {
			task.CompletedAt = &completedAt.Time
		}

		tasks = append(tasks, task)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历核查任务数据失败: %v", err)
	}

	return tasks, nil
}

// UpdateTaskStatus 更新任务状态
func (r *ResultRepository) UpdateTaskStatus(taskID string, status string, progress int) error {
	query := `
		UPDATE check_tasks 
		SET status = ?, progress = ?
		WHERE id = ?
	`

	result, err := r.db.Exec(query, status, progress, taskID)
	if err != nil {
		return fmt.Errorf("更新任务状态失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新行数失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("核查任务不存在")
	}

	return nil
}

// CompleteTask 完成任务
func (r *ResultRepository) CompleteTask(taskID string) error {
	query := `
		UPDATE check_tasks 
		SET status = 'completed', progress = 100, completed_at = ?
		WHERE id = ?
	`

	result, err := r.db.Exec(query, time.Now(), taskID)
	if err != nil {
		return fmt.Errorf("完成任务失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新行数失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("核查任务不存在")
	}

	return nil
}

// CreateResult 创建核查结果
func (r *ResultRepository) CreateResult(result *model.CheckResult) error {
	query := `
		INSERT INTO check_results (task_id, host_id, rule_id, status, actual_result, message, checked_at)
		VALUES (?, ?, ?, ?, ?, ?, ?)
	`

	result.CheckedAt = time.Now()

	res, err := r.db.Exec(query, result.TaskID, result.HostID, result.RuleID,
		result.Status, result.ActualResult, result.Message, result.CheckedAt)
	if err != nil {
		return fmt.Errorf("创建核查结果失败: %v", err)
	}

	id, err := res.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取结果ID失败: %v", err)
	}

	result.ID = int(id)
	return nil
}

// GetResultsByTaskID 根据任务ID获取核查结果
func (r *ResultRepository) GetResultsByTaskID(taskID string) ([]*model.CheckResult, error) {
	query := `
		SELECT id, task_id, host_id, rule_id, status, actual_result, message, checked_at
		FROM check_results WHERE task_id = ? ORDER BY checked_at
	`

	rows, err := r.db.Query(query, taskID)
	if err != nil {
		return nil, fmt.Errorf("查询核查结果失败: %v", err)
	}
	defer rows.Close()

	var results []*model.CheckResult
	for rows.Next() {
		result := &model.CheckResult{}
		err := rows.Scan(
			&result.ID, &result.TaskID, &result.HostID, &result.RuleID,
			&result.Status, &result.ActualResult, &result.Message, &result.CheckedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描核查结果数据失败: %v", err)
		}
		results = append(results, result)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历核查结果数据失败: %v", err)
	}

	return results, nil
}

// GetResultsByHostID 根据主机ID获取核查结果
func (r *ResultRepository) GetResultsByHostID(hostID int) ([]*model.CheckResult, error) {
	query := `
		SELECT id, task_id, host_id, rule_id, status, actual_result, message, checked_at
		FROM check_results WHERE host_id = ? ORDER BY checked_at DESC
	`

	rows, err := r.db.Query(query, hostID)
	if err != nil {
		return nil, fmt.Errorf("查询主机核查结果失败: %v", err)
	}
	defer rows.Close()

	var results []*model.CheckResult
	for rows.Next() {
		result := &model.CheckResult{}
		err := rows.Scan(
			&result.ID, &result.TaskID, &result.HostID, &result.RuleID,
			&result.Status, &result.ActualResult, &result.Message, &result.CheckedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描核查结果数据失败: %v", err)
		}
		results = append(results, result)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历核查结果数据失败: %v", err)
	}

	return results, nil
}

// GetResultsByStatus 根据状态获取核查结果
func (r *ResultRepository) GetResultsByStatus(status string) ([]*model.CheckResult, error) {
	query := `
		SELECT id, task_id, host_id, rule_id, status, actual_result, message, checked_at
		FROM check_results WHERE status = ? ORDER BY checked_at DESC
	`

	rows, err := r.db.Query(query, status)
	if err != nil {
		return nil, fmt.Errorf("查询状态核查结果失败: %v", err)
	}
	defer rows.Close()

	var results []*model.CheckResult
	for rows.Next() {
		result := &model.CheckResult{}
		err := rows.Scan(
			&result.ID, &result.TaskID, &result.HostID, &result.RuleID,
			&result.Status, &result.ActualResult, &result.Message, &result.CheckedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描核查结果数据失败: %v", err)
		}
		results = append(results, result)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历核查结果数据失败: %v", err)
	}

	return results, nil
}

// DeleteTask 删除核查任务
func (r *ResultRepository) DeleteTask(taskID string) error {
	query := "DELETE FROM check_tasks WHERE id = ?"

	result, err := r.db.Exec(query, taskID)
	if err != nil {
		return fmt.Errorf("删除核查任务失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取删除行数失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("核查任务不存在")
	}

	return nil
}

// DeleteResultsByTaskID 删除任务的所有结果
func (r *ResultRepository) DeleteResultsByTaskID(taskID string) error {
	query := "DELETE FROM check_results WHERE task_id = ?"

	_, err := r.db.Exec(query, taskID)
	if err != nil {
		return fmt.Errorf("删除任务结果失败: %v", err)
	}

	return nil
}

// GetTaskStatistics 获取任务统计信息
func (r *ResultRepository) GetTaskStatistics(taskID string) (map[string]int, error) {
	query := `
		SELECT status, COUNT(*) as count
		FROM check_results 
		WHERE task_id = ? 
		GROUP BY status
	`

	rows, err := r.db.Query(query, taskID)
	if err != nil {
		return nil, fmt.Errorf("查询任务统计失败: %v", err)
	}
	defer rows.Close()

	stats := make(map[string]int)
	for rows.Next() {
		var status string
		var count int
		if err := rows.Scan(&status, &count); err != nil {
			return nil, fmt.Errorf("扫描统计数据失败: %v", err)
		}
		stats[status] = count
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历统计数据失败: %v", err)
	}

	return stats, nil
}

// GetRecentTasks 获取最近的任务
func (r *ResultRepository) GetRecentTasks(limit int) ([]*model.CheckTask, error) {
	query := `
		SELECT id, name, type, target_id, status, progress, created_at, completed_at
		FROM check_tasks 
		ORDER BY created_at DESC 
		LIMIT ?
	`

	rows, err := r.db.Query(query, limit)
	if err != nil {
		return nil, fmt.Errorf("查询最近任务失败: %v", err)
	}
	defer rows.Close()

	var tasks []*model.CheckTask
	for rows.Next() {
		task := &model.CheckTask{}
		var completedAt sql.NullTime

		err := rows.Scan(
			&task.ID, &task.Name, &task.Type, &task.TargetID,
			&task.Status, &task.Progress, &task.CreatedAt, &completedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描任务数据失败: %v", err)
		}

		if completedAt.Valid {
			task.CompletedAt = &completedAt.Time
		}

		tasks = append(tasks, task)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历任务数据失败: %v", err)
	}

	return tasks, nil
}
