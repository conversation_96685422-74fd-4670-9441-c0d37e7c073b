package repository

import (
	"database/sql"
	"fmt"
	"time"

	"linux-security-checker/internal/model"
)

// HostRepository 主机数据访问层
type HostRepository struct {
	db *sql.DB
}

// NewHostRepository 创建主机仓库实例
func NewHostRepository(db *sql.DB) *HostRepository {
	return &HostRepository{db: db}
}

// Create 创建主机
func (r *HostRepository) Create(host *model.Host) error {
	query := `
		INSERT INTO hosts (name, ip, port, username, password, private_key, description, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
	`
	
	now := time.Now()
	host.CreatedAt = now
	host.UpdatedAt = now

	result, err := r.db.Exec(query, host.Name, host.IP, host.Port, host.Username, 
		host.Password, host.PrivateKey, host.Description, host.CreatedAt, host.UpdatedAt)
	if err != nil {
		return fmt.Errorf("创建主机失败: %v", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("获取主机ID失败: %v", err)
	}

	host.ID = int(id)
	return nil
}

// GetByID 根据ID获取主机
func (r *HostRepository) GetByID(id int) (*model.Host, error) {
	query := `
		SELECT id, name, ip, port, username, password, private_key, description, created_at, updated_at
		FROM hosts WHERE id = ?
	`

	host := &model.Host{}
	err := r.db.QueryRow(query, id).Scan(
		&host.ID, &host.Name, &host.IP, &host.Port, &host.Username,
		&host.Password, &host.PrivateKey, &host.Description, &host.CreatedAt, &host.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("主机不存在")
		}
		return nil, fmt.Errorf("查询主机失败: %v", err)
	}

	return host, nil
}

// GetByName 根据名称获取主机
func (r *HostRepository) GetByName(name string) (*model.Host, error) {
	query := `
		SELECT id, name, ip, port, username, password, private_key, description, created_at, updated_at
		FROM hosts WHERE name = ?
	`

	host := &model.Host{}
	err := r.db.QueryRow(query, name).Scan(
		&host.ID, &host.Name, &host.IP, &host.Port, &host.Username,
		&host.Password, &host.PrivateKey, &host.Description, &host.CreatedAt, &host.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("主机不存在")
		}
		return nil, fmt.Errorf("查询主机失败: %v", err)
	}

	return host, nil
}

// GetAll 获取所有主机
func (r *HostRepository) GetAll() ([]*model.Host, error) {
	query := `
		SELECT id, name, ip, port, username, password, private_key, description, created_at, updated_at
		FROM hosts ORDER BY created_at DESC
	`

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询主机列表失败: %v", err)
	}
	defer rows.Close()

	var hosts []*model.Host
	for rows.Next() {
		host := &model.Host{}
		err := rows.Scan(
			&host.ID, &host.Name, &host.IP, &host.Port, &host.Username,
			&host.Password, &host.PrivateKey, &host.Description, &host.CreatedAt, &host.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描主机数据失败: %v", err)
		}
		hosts = append(hosts, host)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历主机数据失败: %v", err)
	}

	return hosts, nil
}

// Update 更新主机
func (r *HostRepository) Update(host *model.Host) error {
	query := `
		UPDATE hosts 
		SET name = ?, ip = ?, port = ?, username = ?, password = ?, private_key = ?, description = ?, updated_at = ?
		WHERE id = ?
	`

	host.UpdatedAt = time.Now()

	result, err := r.db.Exec(query, host.Name, host.IP, host.Port, host.Username,
		host.Password, host.PrivateKey, host.Description, host.UpdatedAt, host.ID)
	if err != nil {
		return fmt.Errorf("更新主机失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新行数失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("主机不存在")
	}

	return nil
}

// Delete 删除主机
func (r *HostRepository) Delete(id int) error {
	query := "DELETE FROM hosts WHERE id = ?"

	result, err := r.db.Exec(query, id)
	if err != nil {
		return fmt.Errorf("删除主机失败: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取删除行数失败: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("主机不存在")
	}

	return nil
}

// GetByIDs 根据ID列表获取主机
func (r *HostRepository) GetByIDs(ids []int) ([]*model.Host, error) {
	if len(ids) == 0 {
		return []*model.Host{}, nil
	}

	// 构建IN查询
	query := `
		SELECT id, name, ip, port, username, password, private_key, description, created_at, updated_at
		FROM hosts WHERE id IN (`
	
	args := make([]interface{}, len(ids))
	for i, id := range ids {
		if i > 0 {
			query += ","
		}
		query += "?"
		args[i] = id
	}
	query += ") ORDER BY name"

	rows, err := r.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询主机列表失败: %v", err)
	}
	defer rows.Close()

	var hosts []*model.Host
	for rows.Next() {
		host := &model.Host{}
		err := rows.Scan(
			&host.ID, &host.Name, &host.IP, &host.Port, &host.Username,
			&host.Password, &host.PrivateKey, &host.Description, &host.CreatedAt, &host.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描主机数据失败: %v", err)
		}
		hosts = append(hosts, host)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历主机数据失败: %v", err)
	}

	return hosts, nil
}

// Count 获取主机总数
func (r *HostRepository) Count() (int, error) {
	query := "SELECT COUNT(*) FROM hosts"
	
	var count int
	err := r.db.QueryRow(query).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("获取主机总数失败: %v", err)
	}

	return count, nil
}

// Search 搜索主机
func (r *HostRepository) Search(keyword string) ([]*model.Host, error) {
	query := `
		SELECT id, name, ip, port, username, password, private_key, description, created_at, updated_at
		FROM hosts 
		WHERE name LIKE ? OR ip LIKE ? OR description LIKE ?
		ORDER BY name
	`

	searchPattern := "%" + keyword + "%"
	rows, err := r.db.Query(query, searchPattern, searchPattern, searchPattern)
	if err != nil {
		return nil, fmt.Errorf("搜索主机失败: %v", err)
	}
	defer rows.Close()

	var hosts []*model.Host
	for rows.Next() {
		host := &model.Host{}
		err := rows.Scan(
			&host.ID, &host.Name, &host.IP, &host.Port, &host.Username,
			&host.Password, &host.PrivateKey, &host.Description, &host.CreatedAt, &host.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描主机数据失败: %v", err)
		}
		hosts = append(hosts, host)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历主机数据失败: %v", err)
	}

	return hosts, nil
}
