package repository

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"

	_ "github.com/mattn/go-sqlite3"
)

// Database 数据库连接管理
type Database struct {
	db *sql.DB
}

// NewDatabase 创建新的数据库连接
func NewDatabase(dbPath string) (*Database, error) {
	// 确保数据库目录存在
	dir := filepath.Dir(dbPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("创建数据库目录失败: %v", err)
	}

	// 打开数据库连接
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, fmt.Errorf("打开数据库失败: %v", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %v", err)
	}

	database := &Database{db: db}

	// 初始化数据库表
	if err := database.InitTables(); err != nil {
		return nil, fmt.Errorf("初始化数据库表失败: %v", err)
	}

	return database, nil
}

// GetDB 获取数据库连接
func (d *Database) GetDB() *sql.DB {
	return d.db
}

// Close 关闭数据库连接
func (d *Database) Close() error {
	if d.db != nil {
		return d.db.Close()
	}
	return nil
}

// InitTables 初始化数据库表
func (d *Database) InitTables() error {
	// 创建主机表
	if err := d.createHostTable(); err != nil {
		return err
	}

	// 创建主机组表
	if err := d.createHostGroupTable(); err != nil {
		return err
	}

	// 创建主机组关系表
	if err := d.createHostGroupRelationTable(); err != nil {
		return err
	}

	// 创建核查规则表
	if err := d.createCheckRuleTable(); err != nil {
		return err
	}

	// 创建核查任务表
	if err := d.createCheckTaskTable(); err != nil {
		return err
	}

	// 创建核查结果表
	if err := d.createCheckResultTable(); err != nil {
		return err
	}

	return nil
}

// createHostTable 创建主机表
func (d *Database) createHostTable() error {
	query := `
	CREATE TABLE IF NOT EXISTS hosts (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		name TEXT NOT NULL UNIQUE,
		ip TEXT NOT NULL,
		port INTEGER NOT NULL DEFAULT 22,
		username TEXT NOT NULL,
		password TEXT,
		private_key TEXT,
		description TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	)`

	_, err := d.db.Exec(query)
	if err != nil {
		return fmt.Errorf("创建主机表失败: %v", err)
	}

	// 创建索引
	indexQueries := []string{
		"CREATE INDEX IF NOT EXISTS idx_hosts_name ON hosts(name)",
		"CREATE INDEX IF NOT EXISTS idx_hosts_ip ON hosts(ip)",
	}

	for _, indexQuery := range indexQueries {
		if _, err := d.db.Exec(indexQuery); err != nil {
			return fmt.Errorf("创建主机表索引失败: %v", err)
		}
	}

	return nil
}

// createHostGroupTable 创建主机组表
func (d *Database) createHostGroupTable() error {
	query := `
	CREATE TABLE IF NOT EXISTS host_groups (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		name TEXT NOT NULL UNIQUE,
		description TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	)`

	_, err := d.db.Exec(query)
	if err != nil {
		return fmt.Errorf("创建主机组表失败: %v", err)
	}

	// 创建索引
	if _, err := d.db.Exec("CREATE INDEX IF NOT EXISTS idx_host_groups_name ON host_groups(name)"); err != nil {
		return fmt.Errorf("创建主机组表索引失败: %v", err)
	}

	return nil
}

// createHostGroupRelationTable 创建主机组关系表
func (d *Database) createHostGroupRelationTable() error {
	query := `
	CREATE TABLE IF NOT EXISTS host_group_relations (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		group_id INTEGER NOT NULL,
		host_id INTEGER NOT NULL,
		FOREIGN KEY (group_id) REFERENCES host_groups(id) ON DELETE CASCADE,
		FOREIGN KEY (host_id) REFERENCES hosts(id) ON DELETE CASCADE,
		UNIQUE(group_id, host_id)
	)`

	_, err := d.db.Exec(query)
	if err != nil {
		return fmt.Errorf("创建主机组关系表失败: %v", err)
	}

	// 创建索引
	indexQueries := []string{
		"CREATE INDEX IF NOT EXISTS idx_host_group_relations_group_id ON host_group_relations(group_id)",
		"CREATE INDEX IF NOT EXISTS idx_host_group_relations_host_id ON host_group_relations(host_id)",
	}

	for _, indexQuery := range indexQueries {
		if _, err := d.db.Exec(indexQuery); err != nil {
			return fmt.Errorf("创建主机组关系表索引失败: %v", err)
		}
	}

	return nil
}

// createCheckRuleTable 创建核查规则表
func (d *Database) createCheckRuleTable() error {
	query := `
	CREATE TABLE IF NOT EXISTS check_rules (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		name TEXT NOT NULL UNIQUE,
		category TEXT NOT NULL,
		command TEXT NOT NULL,
		expected_result TEXT NOT NULL,
		description TEXT,
		severity TEXT NOT NULL DEFAULT 'medium',
		enabled BOOLEAN NOT NULL DEFAULT 1
	)`

	_, err := d.db.Exec(query)
	if err != nil {
		return fmt.Errorf("创建核查规则表失败: %v", err)
	}

	// 创建索引
	indexQueries := []string{
		"CREATE INDEX IF NOT EXISTS idx_check_rules_name ON check_rules(name)",
		"CREATE INDEX IF NOT EXISTS idx_check_rules_category ON check_rules(category)",
		"CREATE INDEX IF NOT EXISTS idx_check_rules_severity ON check_rules(severity)",
		"CREATE INDEX IF NOT EXISTS idx_check_rules_enabled ON check_rules(enabled)",
	}

	for _, indexQuery := range indexQueries {
		if _, err := d.db.Exec(indexQuery); err != nil {
			return fmt.Errorf("创建核查规则表索引失败: %v", err)
		}
	}

	return nil
}

// createCheckTaskTable 创建核查任务表
func (d *Database) createCheckTaskTable() error {
	query := `
	CREATE TABLE IF NOT EXISTS check_tasks (
		id TEXT PRIMARY KEY,
		name TEXT NOT NULL,
		type TEXT NOT NULL,
		target_id INTEGER NOT NULL,
		status TEXT NOT NULL DEFAULT 'pending',
		progress INTEGER NOT NULL DEFAULT 0,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		completed_at DATETIME
	)`

	_, err := d.db.Exec(query)
	if err != nil {
		return fmt.Errorf("创建核查任务表失败: %v", err)
	}

	// 创建索引
	indexQueries := []string{
		"CREATE INDEX IF NOT EXISTS idx_check_tasks_status ON check_tasks(status)",
		"CREATE INDEX IF NOT EXISTS idx_check_tasks_type ON check_tasks(type)",
		"CREATE INDEX IF NOT EXISTS idx_check_tasks_created_at ON check_tasks(created_at)",
	}

	for _, indexQuery := range indexQueries {
		if _, err := d.db.Exec(indexQuery); err != nil {
			return fmt.Errorf("创建核查任务表索引失败: %v", err)
		}
	}

	return nil
}

// createCheckResultTable 创建核查结果表
func (d *Database) createCheckResultTable() error {
	query := `
	CREATE TABLE IF NOT EXISTS check_results (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		task_id TEXT NOT NULL,
		host_id INTEGER NOT NULL,
		rule_id INTEGER NOT NULL,
		status TEXT NOT NULL,
		actual_result TEXT,
		message TEXT,
		checked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (task_id) REFERENCES check_tasks(id) ON DELETE CASCADE,
		FOREIGN KEY (host_id) REFERENCES hosts(id) ON DELETE CASCADE,
		FOREIGN KEY (rule_id) REFERENCES check_rules(id) ON DELETE CASCADE
	)`

	_, err := d.db.Exec(query)
	if err != nil {
		return fmt.Errorf("创建核查结果表失败: %v", err)
	}

	// 创建索引
	indexQueries := []string{
		"CREATE INDEX IF NOT EXISTS idx_check_results_task_id ON check_results(task_id)",
		"CREATE INDEX IF NOT EXISTS idx_check_results_host_id ON check_results(host_id)",
		"CREATE INDEX IF NOT EXISTS idx_check_results_rule_id ON check_results(rule_id)",
		"CREATE INDEX IF NOT EXISTS idx_check_results_status ON check_results(status)",
		"CREATE INDEX IF NOT EXISTS idx_check_results_checked_at ON check_results(checked_at)",
	}

	for _, indexQuery := range indexQueries {
		if _, err := d.db.Exec(indexQuery); err != nil {
			return fmt.Errorf("创建核查结果表索引失败: %v", err)
		}
	}

	return nil
}
