package model

import "time"

// Host 主机信息结构体
type Host struct {
	ID          int       `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	IP          string    `json:"ip" db:"ip"`
	Port        int       `json:"port" db:"port"`
	Username    string    `json:"username" db:"username"`
	Password    string    `json:"password,omitempty" db:"password"`
	PrivateKey  string    `json:"private_key,omitempty" db:"private_key"`
	Description string    `json:"description" db:"description"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// HostGroup 主机组结构体
type HostGroup struct {
	ID          int       `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	HostIDs     []int     `json:"host_ids" db:"-"` // 不直接存储在数据库中
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// HostGroupRelation 主机组关系表
type HostGroupRelation struct {
	ID      int `json:"id" db:"id"`
	GroupID int `json:"group_id" db:"group_id"`
	HostID  int `json:"host_id" db:"host_id"`
}
