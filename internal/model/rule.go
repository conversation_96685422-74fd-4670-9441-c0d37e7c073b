package model

import "time"

// CheckRule 核查规则结构体
type CheckRule struct {
	ID             int    `json:"id" db:"id"`
	Name           string `json:"name" db:"name"`
	Category       string `json:"category" db:"category"`
	Command        string `json:"command" db:"command"`
	ExpectedResult string `json:"expected_result" db:"expected_result"`
	Description    string `json:"description" db:"description"`
	Severity       string `json:"severity" db:"severity"` // high, medium, low
	Enabled        bool   `json:"enabled" db:"enabled"`
}

// CheckResult 核查结果结构体
type CheckResult struct {
	ID           int       `json:"id" db:"id"`
	TaskID       string    `json:"task_id" db:"task_id"`
	HostID       int       `json:"host_id" db:"host_id"`
	RuleID       int       `json:"rule_id" db:"rule_id"`
	Status       string    `json:"status" db:"status"` // pass, fail, error
	ActualResult string    `json:"actual_result" db:"actual_result"`
	Message      string    `json:"message" db:"message"`
	CheckedAt    time.Time `json:"checked_at" db:"checked_at"`
}

// CheckTask 核查任务结构体
type CheckTask struct {
	ID          string    `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Type        string    `json:"type" db:"type"` // single, group
	TargetID    int       `json:"target_id" db:"target_id"` // host_id or group_id
	Status      string    `json:"status" db:"status"` // pending, running, completed, failed
	Progress    int       `json:"progress" db:"progress"` // 0-100
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	CompletedAt *time.Time `json:"completed_at,omitempty" db:"completed_at"`
}
