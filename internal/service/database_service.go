package service

import (
	"fmt"
	"os"
	"path/filepath"

	"linux-security-checker/internal/config"
	"linux-security-checker/internal/model"
	"linux-security-checker/internal/repository"
)

// DatabaseService 数据库服务
type DatabaseService struct {
	database *repository.Database
	config   *config.Config
}

// NewDatabaseService 创建数据库服务实例
func NewDatabaseService(cfg *config.Config) (*DatabaseService, error) {
	// 确保数据库目录存在
	dbPath := cfg.Database.Path
	dir := filepath.Dir(dbPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("创建数据库目录失败: %v", err)
	}

	// 创建数据库连接
	database, err := repository.NewDatabase(dbPath)
	if err != nil {
		return nil, fmt.Errorf("初始化数据库失败: %v", err)
	}

	return &DatabaseService{
		database: database,
		config:   cfg,
	}, nil
}

// GetDatabase 获取数据库实例
func (s *DatabaseService) GetDatabase() *repository.Database {
	return s.database
}

// Close 关闭数据库连接
func (s *DatabaseService) Close() error {
	if s.database != nil {
		return s.database.Close()
	}
	return nil
}

// InitializeDefaultData 初始化默认数据
func (s *DatabaseService) InitializeDefaultData() error {
	// 检查是否已有数据
	ruleRepo := repository.NewRuleRepository(s.database.GetDB())
	count, err := ruleRepo.Count()
	if err != nil {
		return fmt.Errorf("检查规则数量失败: %v", err)
	}

	// 如果已有规则，跳过初始化
	if count > 0 {
		return nil
	}

	// 加载默认规则
	if err := s.loadDefaultRules(); err != nil {
		return fmt.Errorf("加载默认规则失败: %v", err)
	}

	return nil
}

// loadDefaultRules 加载默认规则
func (s *DatabaseService) loadDefaultRules() error {
	// 这里可以从YAML文件加载规则，暂时先创建一些示例规则
	ruleRepo := repository.NewRuleRepository(s.database.GetDB())

	defaultRules := []struct {
		name           string
		category       string
		command        string
		expectedResult string
		description    string
		severity       string
	}{
		{
			name:           "检查系统版本信息",
			category:       "系统配置",
			command:        "cat /etc/os-release | grep PRETTY_NAME",
			expectedResult: "contains:Linux",
			description:    "检查操作系统版本信息",
			severity:       "low",
		},
		{
			name:           "检查防火墙状态",
			category:       "系统配置",
			command:        "systemctl is-active firewalld || systemctl is-active ufw",
			expectedResult: "contains:active",
			description:    "检查防火墙服务是否启用",
			severity:       "high",
		},
		{
			name:           "检查root用户状态",
			category:       "用户管理",
			command:        "passwd -S root | awk '{print $2}'",
			expectedResult: "equals:P",
			description:    "检查root用户密码状态",
			severity:       "high",
		},
		{
			name:           "检查空密码用户",
			category:       "用户管理",
			command:        "awk -F: '($2 == \"\") {print $1}' /etc/shadow",
			expectedResult: "empty",
			description:    "检查是否存在空密码用户",
			severity:       "high",
		},
		{
			name:           "检查SSH root登录",
			category:       "网络安全",
			command:        "grep '^PermitRootLogin' /etc/ssh/sshd_config | awk '{print $2}'",
			expectedResult: "equals:no",
			description:    "检查SSH是否禁止root直接登录",
			severity:       "high",
		},
		{
			name:           "检查auditd服务",
			category:       "日志审计",
			command:        "systemctl is-active auditd",
			expectedResult: "contains:active",
			description:    "检查审计服务是否运行",
			severity:       "high",
		},
		{
			name:           "检查passwd文件权限",
			category:       "文件权限",
			command:        "ls -la /etc/passwd | awk '{print $1}'",
			expectedResult: "equals:-rw-r--r--",
			description:    "检查/etc/passwd文件权限",
			severity:       "high",
		},
		{
			name:           "检查shadow文件权限",
			category:       "文件权限",
			command:        "ls -la /etc/shadow | awk '{print $1}'",
			expectedResult: "regex:^-rw-------",
			description:    "检查/etc/shadow文件权限",
			severity:       "high",
		},
	}

	for _, rule := range defaultRules {
		checkRule := &model.CheckRule{
			Name:           rule.name,
			Category:       rule.category,
			Command:        rule.command,
			ExpectedResult: rule.expectedResult,
			Description:    rule.description,
			Severity:       rule.severity,
			Enabled:        true,
		}

		if err := ruleRepo.Create(checkRule); err != nil {
			return fmt.Errorf("创建默认规则失败: %v", err)
		}
	}

	return nil
}

// GetDatabaseInfo 获取数据库信息
func (s *DatabaseService) GetDatabaseInfo() (map[string]interface{}, error) {
	db := s.database.GetDB()
	
	info := make(map[string]interface{})
	
	// 获取各表的记录数
	tables := []string{"hosts", "host_groups", "check_rules", "check_tasks", "check_results"}
	
	for _, table := range tables {
		var count int
		query := fmt.Sprintf("SELECT COUNT(*) FROM %s", table)
		err := db.QueryRow(query).Scan(&count)
		if err != nil {
			return nil, fmt.Errorf("查询表 %s 记录数失败: %v", table, err)
		}
		info[table] = count
	}
	
	// 数据库文件大小
	if stat, err := os.Stat(s.config.Database.Path); err == nil {
		info["database_size"] = stat.Size()
	}
	
	info["database_path"] = s.config.Database.Path
	
	return info, nil
}

// BackupDatabase 备份数据库
func (s *DatabaseService) BackupDatabase(backupPath string) error {
	// 确保备份目录存在
	dir := filepath.Dir(backupPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建备份目录失败: %v", err)
	}

	// 复制数据库文件
	sourceFile, err := os.Open(s.config.Database.Path)
	if err != nil {
		return fmt.Errorf("打开源数据库文件失败: %v", err)
	}
	defer sourceFile.Close()

	destFile, err := os.Create(backupPath)
	if err != nil {
		return fmt.Errorf("创建备份文件失败: %v", err)
	}
	defer destFile.Close()

	// 复制文件内容
	_, err = destFile.ReadFrom(sourceFile)
	if err != nil {
		return fmt.Errorf("复制数据库文件失败: %v", err)
	}

	return nil
}

// RestoreDatabase 恢复数据库
func (s *DatabaseService) RestoreDatabase(backupPath string) error {
	// 检查备份文件是否存在
	if _, err := os.Stat(backupPath); os.IsNotExist(err) {
		return fmt.Errorf("备份文件不存在: %s", backupPath)
	}

	// 关闭当前数据库连接
	if err := s.database.Close(); err != nil {
		return fmt.Errorf("关闭数据库连接失败: %v", err)
	}

	// 复制备份文件到数据库路径
	backupFile, err := os.Open(backupPath)
	if err != nil {
		return fmt.Errorf("打开备份文件失败: %v", err)
	}
	defer backupFile.Close()

	destFile, err := os.Create(s.config.Database.Path)
	if err != nil {
		return fmt.Errorf("创建数据库文件失败: %v", err)
	}
	defer destFile.Close()

	// 复制文件内容
	_, err = destFile.ReadFrom(backupFile)
	if err != nil {
		return fmt.Errorf("恢复数据库文件失败: %v", err)
	}

	// 重新打开数据库连接
	database, err := repository.NewDatabase(s.config.Database.Path)
	if err != nil {
		return fmt.Errorf("重新连接数据库失败: %v", err)
	}

	s.database = database
	return nil
}
