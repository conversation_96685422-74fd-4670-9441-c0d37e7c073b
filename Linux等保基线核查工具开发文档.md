# Linux等保基线核查工具开发文档

## 1. 项目概述

### 1.1 项目名称
Linux等保基线核查工具

### 1.2 项目目标
开发一款基于Go语言和Fyne GUI框架的Linux等保基线核查工具，支持单主机和多主机核查，提供直观的图形界面和完整的核查结果导出功能。

### 1.3 核心功能
- 单主机核查：对单台Linux主机进行等保基线核查
- 多主机核查：创建主机组，批量核查多台主机
- 结果管理：查看、分析和导出核查结果
- 侧边栏导航：提供清晰的功能导航界面

## 2. 技术架构

### 2.1 技术栈
- **编程语言**: Go 1.19+
- **GUI框架**: Fyne v2.4+
- **SSH连接**: golang.org/x/crypto/ssh
- **数据存储**: SQLite3 / JSON文件
- **并发处理**: Go Goroutines
- **配置管理**: YAML/JSON

### 2.2 架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GUI Layer     │    │  Business Layer │    │   Data Layer    │
│                 │    │                 │    │                 │
│ - Fyne界面      │◄──►│ - 主机管理      │◄──►│ - SQLite数据库  │
│ - 侧边栏导航    │    │ - 核查引擎      │    │ - 配置文件      │
│ - 结果展示      │    │ - 结果处理      │    │ - 日志文件      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 3. 功能模块设计

### 3.1 主机管理模块
- **单主机管理**
  - 添加/编辑/删除主机信息
  - SSH连接配置（IP、端口、用户名、密码/密钥）
  - 连接测试功能

- **多主机组管理**
  - 创建/编辑/删除主机组
  - 向组中添加/移除主机
  - 组的批量操作

### 3.2 等保基线核查模块
- **核查规则管理**
  - 预定义等保基线规则
  - 自定义规则配置
  - 规则分类管理

- **核查执行引擎**
  - SSH远程连接
  - 命令执行和结果收集
  - 并发核查支持
  - 进度监控

### 3.3 结果管理模块
- **结果存储**
  - 核查结果持久化
  - 历史记录管理
  - 结果对比分析

- **结果导出**
  - PDF报告生成
  - Excel表格导出
  - HTML报告生成
  - 自定义模板支持

## 4. 数据结构设计

### 4.1 主机信息
```go
type Host struct {
    ID          int       `json:"id"`
    Name        string    `json:"name"`
    IP          string    `json:"ip"`
    Port        int       `json:"port"`
    Username    string    `json:"username"`
    Password    string    `json:"password,omitempty"`
    PrivateKey  string    `json:"private_key,omitempty"`
    Description string    `json:"description"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

### 4.2 主机组
```go
type HostGroup struct {
    ID          int       `json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    HostIDs     []int     `json:"host_ids"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

### 4.3 核查规则
```go
type CheckRule struct {
    ID          int    `json:"id"`
    Name        string `json:"name"`
    Category    string `json:"category"`
    Command     string `json:"command"`
    ExpectedResult string `json:"expected_result"`
    Description string `json:"description"`
    Severity    string `json:"severity"` // high, medium, low
}
```

### 4.4 核查结果
```go
type CheckResult struct {
    ID          int       `json:"id"`
    TaskID      string    `json:"task_id"`
    HostID      int       `json:"host_id"`
    RuleID      int       `json:"rule_id"`
    Status      string    `json:"status"` // pass, fail, error
    ActualResult string   `json:"actual_result"`
    Message     string    `json:"message"`
    CheckedAt   time.Time `json:"checked_at"`
}
```

## 5. GUI界面设计

### 5.1 主窗口布局
使用Fyne的Border布局实现侧边栏导航：

```go
// 主窗口布局
func createMainWindow() fyne.Window {
    // 创建侧边栏
    sidebar := createSidebar()
    
    // 创建主内容区域
    content := createMainContent()
    
    // 使用Border布局
    mainContainer := container.NewBorder(
        nil,        // top
        nil,        // bottom  
        sidebar,    // left (侧边栏)
        nil,        // right
        content,    // center (主内容)
    )
    
    window := myApp.NewWindow("Linux等保基线核查工具")
    window.SetContent(mainContainer)
    return window
}
```

### 5.2 侧边栏导航
```go
func createSidebar() *fyne.Container {
    // 导航按钮
    hostMgmtBtn := widget.NewButton("主机管理", func() {
        showHostManagement()
    })
    
    groupMgmtBtn := widget.NewButton("组管理", func() {
        showGroupManagement()  
    })
    
    checkBtn := widget.NewButton("开始核查", func() {
        showCheckInterface()
    })
    
    resultBtn := widget.NewButton("核查结果", func() {
        showResults()
    })
    
    exportBtn := widget.NewButton("导出报告", func() {
        showExportInterface()
    })
    
    // 垂直布局
    return container.NewVBox(
        widget.NewLabel("功能导航"),
        widget.NewSeparator(),
        hostMgmtBtn,
        groupMgmtBtn,
        widget.NewSeparator(),
        checkBtn,
        resultBtn,
        widget.NewSeparator(),
        exportBtn,
    )
}
```

## 6. 开发环境和依赖

### 6.1 开发环境
- Go 1.19+
- Git
- IDE: VS Code / GoLand

### 6.2 主要依赖
```go
require (
    fyne.io/fyne/v2 v2.4.0
    golang.org/x/crypto v0.14.0
    github.com/mattn/go-sqlite3 v1.14.17
    gopkg.in/yaml.v3 v3.0.1
    github.com/jung-kurt/gofpdf v1.16.2
    github.com/xuri/excelize/v2 v2.8.0
)
```

## 7. 项目结构
```
linux-security-checker/
├── cmd/
│   └── main.go                 # 程序入口
├── internal/
│   ├── gui/                    # GUI相关
│   │   ├── window.go          # 主窗口
│   │   ├── sidebar.go         # 侧边栏
│   │   ├── host.go            # 主机管理界面
│   │   ├── group.go           # 组管理界面
│   │   ├── check.go           # 核查界面
│   │   └── result.go          # 结果界面
│   ├── model/                  # 数据模型
│   │   ├── host.go
│   │   ├── group.go
│   │   ├── rule.go
│   │   └── result.go
│   ├── service/                # 业务逻辑
│   │   ├── host_service.go
│   │   ├── check_service.go
│   │   └── export_service.go
│   ├── repository/             # 数据访问
│   │   ├── host_repo.go
│   │   └── result_repo.go
│   └── config/                 # 配置管理
│       └── config.go
├── assets/                     # 资源文件
│   ├── rules/                  # 等保基线规则
│   └── templates/              # 报告模板
├── docs/                       # 文档
├── go.mod
└── go.sum
```

## 8. 开发计划

### 阶段一：基础框架搭建（1-2周）
- 项目初始化和依赖管理
- 基础GUI框架搭建
- 侧边栏导航实现
- 数据库设计和初始化

### 阶段二：主机管理功能（1周）
- 单主机CRUD操作
- 主机组管理
- SSH连接测试

### 阶段三：核查引擎开发（2-3周）
- 等保基线规则定义
- SSH远程执行引擎
- 并发核查实现
- 进度监控

### 阶段四：结果管理和导出（1-2周）
- 结果展示界面
- 多格式导出功能
- 报告模板设计

### 阶段五：测试和优化（1周）
- 功能测试
- 性能优化
- 用户体验改进

## 9. 注意事项

1. **安全性**：SSH连接信息需要加密存储
2. **并发控制**：多主机核查时需要控制并发数量
3. **错误处理**：完善的错误处理和日志记录
4. **用户体验**：提供清晰的进度提示和操作反馈
5. **扩展性**：预留规则扩展和插件接口

---

*本文档将随着开发进度持续更新和完善*
