// +build !nogui

package main

import (
	"fmt"
	"log"

	"linux-security-checker/internal/config"
	"linux-security-checker/internal/gui"
	"linux-security-checker/internal/service"
)

func main() {
	fmt.Println("Linux等保基线核查工具启动中...")

	// 加载配置
	cfg, err := config.LoadConfig("./config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化数据库服务
	dbService, err := service.NewDatabaseService(cfg)
	if err != nil {
		log.Fatalf("初始化数据库服务失败: %v", err)
	}
	defer dbService.Close()

	// 初始化默认数据
	if err := dbService.InitializeDefaultData(); err != nil {
		log.Fatalf("初始化默认数据失败: %v", err)
	}

	// 创建GUI应用
	app := gui.NewApp(cfg, dbService)

	// 初始化界面
	if err := app.Initialize(); err != nil {
		log.Fatalf("初始化GUI失败: %v", err)
	}

	fmt.Println("✅ 应用启动成功")

	// 运行应用
	app.Run()
}

// 创建侧边栏
func createSidebar() *fyne.Container {
	// 导航按钮
	hostMgmtBtn := widget.NewButton("主机管理", func() {
		fmt.Println("点击了主机管理")
	})

	groupMgmtBtn := widget.NewButton("组管理", func() {
		fmt.Println("点击了组管理")
	})

	checkBtn := widget.NewButton("开始核查", func() {
		fmt.Println("点击了开始核查")
	})

	resultBtn := widget.NewButton("核查结果", func() {
		fmt.Println("点击了核查结果")
	})

	exportBtn := widget.NewButton("导出报告", func() {
		fmt.Println("点击了导出报告")
	})

	// 设置按钮样式
	buttons := []*widget.Button{hostMgmtBtn, groupMgmtBtn, checkBtn, resultBtn, exportBtn}
	for _, btn := range buttons {
		btn.Resize(fyne.NewSize(150, 40))
	}

	// 垂直布局
	return container.NewVBox(
		widget.NewCard("", "功能导航", container.NewVBox(
			hostMgmtBtn,
			widget.NewSeparator(),
			groupMgmtBtn,
			widget.NewSeparator(),
			checkBtn,
			widget.NewSeparator(),
			resultBtn,
			widget.NewSeparator(),
			exportBtn,
		)),
	)
}
