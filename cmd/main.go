// +build !nogui

package main

import (
	"fmt"
	"log"

	"linux-security-checker/internal/config"
	"linux-security-checker/internal/gui"
	"linux-security-checker/internal/service"
)

func main() {
	fmt.Println("Linux等保基线核查工具启动中...")

	// 加载配置
	cfg, err := config.LoadConfig("./config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化数据库服务
	dbService, err := service.NewDatabaseService(cfg)
	if err != nil {
		log.Fatalf("初始化数据库服务失败: %v", err)
	}
	defer dbService.Close()

	// 初始化默认数据
	if err := dbService.InitializeDefaultData(); err != nil {
		log.Fatalf("初始化默认数据失败: %v", err)
	}

	// 创建GUI应用
	app := gui.NewApp(cfg, dbService)

	// 初始化界面
	if err := app.Initialize(); err != nil {
		log.Fatalf("初始化GUI失败: %v", err)
	}

	fmt.Println("✅ 应用启动成功")

	// 运行应用
	app.Run()
}


