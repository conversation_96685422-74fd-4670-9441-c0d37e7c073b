// +build !nogui

package main

import (
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
)

func main() {
	fmt.Println("Linux等保基线核查工具启动中...")

	// 创建Fyne应用
	myApp := app.New()
	myApp.SetMetadata(&fyne.AppMetadata{
		ID:   "com.security.checker",
		Name: "Linux等保基线核查工具",
	})

	// 创建主窗口
	myWindow := myApp.NewWindow("Linux等保基线核查工具")
	myWindow.Resize(fyne.NewSize(1200, 800))

	// 创建侧边栏
	sidebar := createSidebar()

	// 创建主内容区域
	mainContent := widget.NewLabel("欢迎使用Linux等保基线核查工具\n\n请从左侧菜单选择功能")
	mainContent.Alignment = fyne.TextAlignCenter

	// 使用Border布局
	content := container.NewBorder(
		nil,        // top
		nil,        // bottom
		sidebar,    // left (侧边栏)
		nil,        // right
		mainContent, // center (主内容)
	)

	myWindow.SetContent(content)
	myWindow.ShowAndRun()
}

// 创建侧边栏
func createSidebar() *fyne.Container {
	// 导航按钮
	hostMgmtBtn := widget.NewButton("主机管理", func() {
		fmt.Println("点击了主机管理")
	})

	groupMgmtBtn := widget.NewButton("组管理", func() {
		fmt.Println("点击了组管理")
	})

	checkBtn := widget.NewButton("开始核查", func() {
		fmt.Println("点击了开始核查")
	})

	resultBtn := widget.NewButton("核查结果", func() {
		fmt.Println("点击了核查结果")
	})

	exportBtn := widget.NewButton("导出报告", func() {
		fmt.Println("点击了导出报告")
	})

	// 设置按钮样式
	buttons := []*widget.Button{hostMgmtBtn, groupMgmtBtn, checkBtn, resultBtn, exportBtn}
	for _, btn := range buttons {
		btn.Resize(fyne.NewSize(150, 40))
	}

	// 垂直布局
	return container.NewVBox(
		widget.NewCard("", "功能导航", container.NewVBox(
			hostMgmtBtn,
			widget.NewSeparator(),
			groupMgmtBtn,
			widget.NewSeparator(),
			checkBtn,
			widget.NewSeparator(),
			resultBtn,
			widget.NewSeparator(),
			exportBtn,
		)),
	)
}
