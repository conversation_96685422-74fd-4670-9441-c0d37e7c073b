package main

import (
	"fmt"
	"log"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/widget"
)

func main() {
	fmt.Println("Linux等保基线核查工具启动中...")
	
	// 创建Fyne应用
	myApp := app.New()
	myApp.SetMetadata(&fyne.AppMetadata{
		ID:   "com.security.checker",
		Name: "Linux等保基线核查工具",
	})
	
	// 创建主窗口
	myWindow := myApp.NewWindow("Linux等保基线核查工具")
	myWindow.Resize(fyne.NewSize(1200, 800))
	
	// 临时内容，后续会替换为完整的GUI
	content := widget.NewLabel("Linux等保基线核查工具")
	content.Alignment = fyne.TextAlignCenter
	
	myWindow.SetContent(content)
	myWindow.ShowAndRun()
}
