// +build nogui

package main

import (
	"fmt"
	"os"
)

func main() {
	fmt.Println("=================================")
	fmt.Println("   Linux等保基线核查工具")
	fmt.Println("   版本: v1.0.0")
	fmt.Println("   简化版本 (命令行模式)")
	fmt.Println("=================================")
	fmt.Println()
	
	fmt.Println("功能列表:")
	fmt.Println("1. 主机管理")
	fmt.Println("2. 组管理") 
	fmt.Println("3. 开始核查")
	fmt.Println("4. 核查结果")
	fmt.Println("5. 导出报告")
	fmt.Println()
	
	fmt.Println("注意: 这是简化版本，完整GUI功能请使用完整版本")
	fmt.Println()
	
	// 等待用户输入
	fmt.Print("按回车键退出...")
	fmt.Scanln()
	
	os.Exit(0)
}
